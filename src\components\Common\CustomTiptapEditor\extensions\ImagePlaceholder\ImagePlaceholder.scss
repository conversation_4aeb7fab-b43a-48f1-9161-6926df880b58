.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  min-height: 120px;
  position: relative;
  
  &-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-align: center;
    padding: 16px;
  }
  
  &-text {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
  }
  
  &-icon {
    font-size: 24px;
    opacity: 0.7;
  }
  
  &-error-icon {
    font-size: 24px;
    color: #dc3545;
  }
  
  &-retry-btn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background: #0056b3;
    }
    
    &:focus {
      outline: 2px solid #007bff;
      outline-offset: 2px;
    }
  }
  
  .spinner-border-sm {
    width: 1.5rem;
    height: 1.5rem;
    color: #007bff;
  }
}

import { RiAddLine } from "@remixicon/react";
import { useGetReports } from "features/Reports/api";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "react-bootstrap";
import { LoaderIcon } from "react-hot-toast";
import InfiniteScroll from "react-infinite-scroll-component";
import { Link, useParams } from "react-router-dom";
import HistoryItemAction from "./HistoryItemAction";
import "./styles.scss";

const HistoryBox = ({ handleBuildReportClick }: any) => {
  const [page, setPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [historyList, setHistoryList] = useState<Record<string, any>[]>([]);
  const [openItemId, setOpenItemId] = useState<any>(null);

  const scrollableDivRef = useRef<HTMLDivElement>(null);
  // const tempNewChat = useChatStore((state) => state.tempNewChat);
  const { id: reportId } = useParams();

  const { data: { data: reportsData = [], count } = {}, isFetching } = useGetReports({
    params: {
      page,
      limit: 30,
    },
  });

  useEffect(() => {
    if (reportsData?.length) {
      setHistoryList((prevList) => {
        if (page === 1) {
          // Reset list for first page
          return reportsData;
        } else {
          // Append new data for subsequent pages, avoiding duplicates
          const uniqueItems = reportsData.filter(
            (newItem: any) =>
              !prevList.some((existing: any) => existing.id === newItem.id)
          );
          return [...prevList, ...uniqueItems];
        }
      });
      setTotalCount(count || 0);
    }
  }, [reportsData, page, count]);

  // Reset pagination when component mounts
  useEffect(() => {
    setPage(1);
    setHistoryList([]);
    setTotalCount(0);
  }, []);



  const handleNext = () => {
    if (historyList.length >= totalCount || isFetching) return;
    setPage((prevPage) => prevPage + 1);
  };

  const refreshReports = () => {
    setPage(1);
    setHistoryList([]);
    setTotalCount(0);
  };

  return (
    <div className="history-box bg-white h-100">
      <div className="ps-3 mb-3 start-new-chat">
        <Button
          variant="primary"
          className="w-100 py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-between align-items-center"
          onClick={handleBuildReportClick}
        >
          Build New Report
          <RiAddLine size={"22px"} color="#f9f9f9" />
        </Button>
      </div>

      <div
        className="history-box-listing-wrapper position-relative d-flex flex-column overflow-auto"
        id="scrollableDiv"
        ref={scrollableDivRef}
      >
        <InfiniteScroll
          dataLength={historyList.length}
          next={handleNext}
          hasMore={totalCount > 0 && historyList.length < totalCount}
          loader={
            <div className="d-flex align-items-center justify-content-center py-2">
              <LoaderIcon />
            </div>
          }
          scrollableTarget="scrollableDiv"
          style={{ overflow: 'visible' }}
        >
          <div className="history-box-listing-wrapper-data">
            <ul className="list-unstyled m-0 history-box-listing-wrapper-data-list d-flex flex-column gap-1">
              {historyList.map((item) => (
                <li
                  className={`position-relative list-item ${reportId === item?.id ? "active-chat" : ""}`}
                  key={item?.id}
                >
                  <div className="list-item-wrapper overflow-hidden">
                    <Link
                      to={`${REPORTS_ROUTE_PATH.REPORTS}/${item?.id}`}
                      className="mb-0 text-decoration-none list-unstyled m-0 list-item-wrapper-description position-relative d-block text-truncate text-capitalize"
                    >
                      {item?.title?.toLowerCase()}
                    </Link>
                    <HistoryItemAction
                      itemInfo={item}
                      openItemId={openItemId}
                      setOpenItemId={setOpenItemId}
                      setHistoryList={setHistoryList}
                      setTotalCount={setTotalCount}
                      refreshReports={refreshReports}
                    />
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default HistoryBox;

import { RiAddLine } from "@remixicon/react";
import { useGetReports } from "features/Reports/api";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "react-bootstrap";
import { LoaderIcon } from "react-hot-toast";
import InfiniteScroll from "react-infinite-scroll-component";
import { Link, useParams } from "react-router-dom";
import useChatStore from "stores/chat";
import HistoryItemAction from "./HistoryItemAction";
import "./styles.scss";

const HistoryBox = ({ handleBuildReportClick }: any) => {
  const [page, setPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [historyList, setHistoryList] = useState<Record<string, any>[]>([]);
  const [openItemId, setOpenItemId] = useState<any>(null);

  const scrollableDivRef = useRef<HTMLDivElement>(null);
  const tempNewChat = useChatStore((state) => state.tempNewChat);
  const { id: reportId } = useParams();

  const { data: { data: reportsData = [], count } = {}, refetch } =
    useGetReports({
      page,
      limit: 30,
    });

  useEffect(() => {
    if (reportsData?.length) {
      const element = scrollableDivRef.current;
      const previousScrollHeight = element?.scrollHeight || 0;
      const previousScrollTop = element?.scrollTop || 0;

      setHistoryList((prevList) => {
        if (page === 1) {
          return reportsData;
        } else {
          const uniqueChats = reportsData.filter(
            (newChat: any) =>
              !prevList.some(
                (existingChat: any) => existingChat.chat_id === newChat.chat_id
              )
          );
          return [...prevList, ...uniqueChats];
        }
      });
      setTotalCount(count);

      if (element) {
        const newScrollHeight = element.scrollHeight;
        element.scrollTop =
          previousScrollTop + (newScrollHeight - previousScrollHeight);
      }
    }
  }, [reportsData, page]);

  useEffect(() => {
    refetch();
  }, [page]);

  useEffect(() => {
    if (tempNewChat?.chat_id) {
      setHistoryList((prevList) => {
        const chatExists = prevList.some(
          (chat) => chat.chat_id === tempNewChat.chat_id
        );
        if (chatExists) {
          return prevList.map((chat) =>
            chat.chat_id === tempNewChat.chat_id ? tempNewChat : chat
          );
        }
        return [tempNewChat, ...prevList];
      });
    }
  }, [tempNewChat?.chat_id]);

  const handleNext = () => {
    setTimeout(() => {
      setPage(page + 1);
    }, 1000);
  };

  return (
    <div className="history-box bg-white h-100">
      <div className="ps-3 mb-3 start-new-chat">
        <Button
          variant="primary"
          className="w-100 py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-between align-items-center"
          onClick={handleBuildReportClick}
        >
          Build New Report
          <RiAddLine size={"22px"} color="#f9f9f9" />
        </Button>
      </div>

      <div
        className="history-box-listing-wrapper position-relative d-flex flex-column overflow-auto"
        id="scrollableDiv"
        ref={scrollableDivRef}
      >
        <InfiniteScroll
          dataLength={historyList?.length}
          loader={
            <div className="d-flex align-items-center justify-content-center">
              <LoaderIcon />
            </div>
          }
          hasMore={historyList?.length < totalCount}
          next={handleNext}
          scrollableTarget={"scrollableDiv"}
          className="h-100"
        >
          <div className="history-box-listing-wrapper-data">
            {/* <p className="history-box-listing-wrapper-data-time mb-1 text-capitalize fw-bold position-sticky top-0">
              Template Reports
            </p> */}

            <ul className="list-unstyled m-0 history-box-listing-wrapper-data-list d-flex flex-column gap-1">
              {historyList.map((item, idx) => (
                <li
                  className={`position-relative list-item ${reportId === item?.id ? "active-chat" : ""}`}
                  key={item?.id}
                >
                  <div className="list-item-wrapper overflow-hidden">
                    <Link
                      to={`${REPORTS_ROUTE_PATH.REPORTS}/${item?.id}`}
                      className="mb-0 text-decoration-none list-unstyled m-0 list-item-wrapper-description position-relative d-block text-truncate text-capitalize"
                    >
                      {item?.title?.toLowerCase()}
                    </Link>
                    <HistoryItemAction
                      itemInfo={item}
                      openItemId={openItemId}
                      setOpenItemId={setOpenItemId}
                      setHistoryList={setHistoryList}
                      setTotalCount={setTotalCount}
                    />
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default HistoryBox;

import Image from "@tiptap/extension-image";

export const ImageResize = Image.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      assetId: {
        default: null,
        parseHTML: (element) => element.getAttribute("data-asset-id"),
        renderHTML: (attributes) => {
          if (!attributes.assetId) return {};
          return { "data-asset-id": attributes.assetId };
        },
      },
      style: {
        default: "height: auto; cursor: pointer;",
        parseHTML: (element) => {
          const width = element.getAttribute("width");
          return width
            ? `width: ${width}px; height: auto; cursor: pointer;`
            : `${element.style.cssText}`;
        },
      },
    };
  },
  addOptions() {
    return {
      ...this.parent?.(),
      onDelete: undefined,
      onUrlRefresh: undefined,
    };
  },

  addNodeView() {
    return ({ node, editor, getPos }) => {
      const {
        view,
        options: { editable },
      } = editor;
      const { style, assetId, src } = node.attrs;
      const $wrapper = document.createElement("div");
      const $container = document.createElement("div");
      const $img = document.createElement("img");
      const $placeholder = document.createElement("div");
      const iconStyle = "width: 24px; height: 24px; cursor: pointer;";

      let isRefreshing = false;

      const dispatchNodeView = () => {
        if (typeof getPos === "function") {
          const newAttrs = {
            ...node.attrs,
            style: `${$img.style.cssText}`,
          };
          view.dispatch(view.state.tr.setNodeMarkup(getPos(), null, newAttrs));
        }
      };

      // Create placeholder content
      const createPlaceholder = (isLoading = false, error = null) => {
        $placeholder.innerHTML = `
          <div style="
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 16px;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            min-height: 120px;
            text-align: center;
          ">
            ${
              isLoading
                ? `
              <div style="
                width: 1.5rem;
                height: 1.5rem;
                border: 2px solid #007bff;
                border-top: 2px solid transparent;
                border-radius: 50%;
                animation: spin 1s linear infinite;
              "></div>
              <span style="font-size: 14px; color: #6c757d; font-weight: 500;">Loading image...</span>
            `
                : error
                  ? `
              <div style="font-size: 24px; color: #dc3545;">⚠️</div>
              <span style="font-size: 14px; color: #6c757d; font-weight: 500;">Failed to load image</span>
              <button onclick="this.parentElement.parentElement.dispatchEvent(new CustomEvent('retry'))" style="
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                cursor: pointer;
              ">Retry</button>
            `
                  : `
              <div style="font-size: 24px; opacity: 0.7;">🖼️</div>
              <span style="font-size: 14px; color: #6c757d; font-weight: 500;">Image placeholder</span>
            `
            }
          </div>
        `;
      };

      // Add CSS animation for spinner
      if (!document.getElementById("image-spinner-animation")) {
        const style = document.createElement("style");
        style.id = "image-spinner-animation";
        style.textContent = `
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        document.head.appendChild(style);
      }

      // Handle URL refresh
      const refreshImageUrl = async () => {
        if (!assetId || isRefreshing || !this.options.onUrlRefresh) return;

        isRefreshing = true;
        $img.style.display = "none";
        $placeholder.style.display = "block";
        createPlaceholder(true);

        try {
          await this.options.onUrlRefresh(assetId, (newUrl) => {
            if (newUrl) {
              $img.src = newUrl;
              $img.style.display = "block";
              $placeholder.style.display = "none";

              // Update node attributes with new URL
              if (typeof getPos === "function") {
                const newAttrs = {
                  ...node.attrs,
                  src: newUrl,
                };
                view.dispatch(
                  view.state.tr.setNodeMarkup(getPos(), null, newAttrs)
                );
              }
            }
            isRefreshing = false;
          });
        } catch (error) {
          console.error("Failed to refresh image URL:", error);
          createPlaceholder(false, error);
          isRefreshing = false;
        }
      };

      const isUrlLikelyExpired = (url: string): boolean => {
        if (!url) return false;

        // Check for common S3 URL patterns that might expire
        const s3UrlPattern = /amazonaws\.com.*[?&]X-Amz-Expires=/;
        const hasExpiryParam = /[?&](expires?|exp)=/i;

        return s3UrlPattern.test(url) || hasExpiryParam.test(url);
      };

      const paintPositionContoller = () => {
        const $postionController = document.createElement("div");

        const $leftController = document.createElement("img");
        const $centerController = document.createElement("img");
        const $rightController = document.createElement("img");
        const $deleteController = document.createElement("img");

        const controllerMouseOver = (e) => {
          e.target.style.opacity = 0.3;
        };

        const controllerMouseOut = (e) => {
          e.target.style.opacity = 1;
        };

        $postionController.setAttribute(
          "style",
          "position: absolute; top: 0%; left: 50%; height: 25px; z-index: 999; background-color: #fff; border-radius: 4px; cursor: pointer; transform: translate(-50%, -50%); display: flex; justify-content: space-between; align-items: center; padding: 0 10px;"
        );

        $leftController.setAttribute(
          "src",
          "https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_left/default/20px.svg"
        );
        $leftController.setAttribute("style", iconStyle);
        $leftController.addEventListener("mouseover", controllerMouseOver);
        $leftController.addEventListener("mouseout", controllerMouseOut);

        $centerController.setAttribute(
          "src",
          "https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_center/default/20px.svg"
        );
        $centerController.setAttribute("style", iconStyle);
        $centerController.addEventListener("mouseover", controllerMouseOver);
        $centerController.addEventListener("mouseout", controllerMouseOut);

        $rightController.setAttribute(
          "src",
          "https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_right/default/20px.svg"
        );
        $rightController.setAttribute("style", iconStyle);
        $rightController.addEventListener("mouseover", controllerMouseOver);
        $rightController.addEventListener("mouseout", controllerMouseOut);

        $deleteController.setAttribute(
          "src",
          "https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/delete/default/20px.svg"
        );
        $deleteController.setAttribute("style", iconStyle);
        $deleteController.addEventListener("mouseover", controllerMouseOver);
        $deleteController.addEventListener("mouseout", controllerMouseOut);

        $leftController.addEventListener("click", () => {
          $img.setAttribute(
            "style",
            `${$img.style.cssText} margin: 0 auto 0 0;`
          );
          dispatchNodeView();
        });
        $centerController.addEventListener("click", () => {
          $img.setAttribute("style", `${$img.style.cssText} margin: 0 auto;`);
          dispatchNodeView();
        });
        $rightController.addEventListener("click", () => {
          $img.setAttribute(
            "style",
            `${$img.style.cssText} margin: 0 0 0 auto;`
          );
          dispatchNodeView();
        });
        $deleteController.addEventListener("click", () => {
          const assetId = node.attrs.assetId;
          const pos = getPos();
          this.options.onDelete({ pos, assetId, editor });
        });

        $postionController.appendChild($leftController);
        $postionController.appendChild($centerController);
        $postionController.appendChild($rightController);
        $postionController.appendChild($deleteController);

        $container.appendChild($postionController);
      };

      // Setup image error handling
      $img.addEventListener("error", () => {
        if (assetId && isUrlLikelyExpired(src) && !isRefreshing) {
          refreshImageUrl();
        } else {
          $img.style.display = "none";
          $placeholder.style.display = "block";
          createPlaceholder(false, "Failed to load image");
        }
      });

      // Setup image load success
      $img.addEventListener("load", () => {
        $img.style.display = "block";
        $placeholder.style.display = "none";
      });

      // Setup placeholder retry
      $placeholder.addEventListener("retry", () => {
        if (assetId) {
          refreshImageUrl();
        }
      });

      $wrapper.setAttribute("style", `display: flex;`);
      $wrapper.appendChild($container);

      $container.setAttribute("style", `${style}`);
      $container.appendChild($img);
      $container.appendChild($placeholder);

      // Initially hide placeholder
      $placeholder.style.display = "none";

      Object.entries(node.attrs).forEach(([key, value]) => {
        if (value === undefined || value === null) return;
        $img.setAttribute(key, value);
      });

      // Check if URL might be expired on initial load
      if (assetId && src && isUrlLikelyExpired(src)) {
        refreshImageUrl();
      }

      if (!editable) return { dom: $container };
      const isMobile = document.documentElement.clientWidth < 768;
      const dotPosition = isMobile ? "-8px" : "-4px";
      const dotsPosition = [
        `top: ${dotPosition}; left: ${dotPosition}; cursor: nwse-resize;`,
        `top: ${dotPosition}; right: ${dotPosition}; cursor: nesw-resize;`,
        `bottom: ${dotPosition}; left: ${dotPosition}; cursor: nesw-resize;`,
        `bottom: ${dotPosition}; right: ${dotPosition}; cursor: nwse-resize;`,
      ];

      let isResizing = false;
      let startX: number, startWidth: number;

      $container.addEventListener("click", (e) => {
        //remove remaining dots and position controller
        const isMobile = document.documentElement.clientWidth < 768;
        isMobile &&
          (
            document.querySelector(".ProseMirror-focused") as HTMLElement
          )?.blur();

        if ($container.childElementCount > 3) {
          for (let i = 0; i < 5; i++) {
            $container.removeChild($container.lastChild as Node);
          }
        }

        paintPositionContoller();

        $container.setAttribute(
          "style",
          `position: relative; border: 1px dashed #6C6C6C; ${style} cursor: pointer;`
        );

        Array.from({ length: 4 }, (_, index) => {
          const $dot = document.createElement("div");
          $dot.setAttribute(
            "style",
            `position: absolute; width: ${isMobile ? 16 : 9}px; height: ${isMobile ? 16 : 9}px; border: 1.5px solid #6C6C6C; border-radius: 50%; ${dotsPosition[index]}`
          );

          $dot.addEventListener("mousedown", (e) => {
            e.preventDefault();
            isResizing = true;
            startX = e.clientX;
            startWidth = $container.offsetWidth;

            const onMouseMove = (e: MouseEvent) => {
              if (!isResizing) return;
              const deltaX =
                index % 2 === 0 ? -(e.clientX - startX) : e.clientX - startX;

              const newWidth = startWidth + deltaX;

              $container.style.width = newWidth + "px";

              $img.style.width = newWidth + "px";
            };

            const onMouseUp = () => {
              if (isResizing) {
                isResizing = false;
              }
              dispatchNodeView();

              document.removeEventListener("mousemove", onMouseMove);
              document.removeEventListener("mouseup", onMouseUp);
            };

            document.addEventListener("mousemove", onMouseMove);
            document.addEventListener("mouseup", onMouseUp);
          });

          $dot.addEventListener(
            "touchstart",
            (e) => {
              e.cancelable && e.preventDefault();
              isResizing = true;
              startX = e.touches[0].clientX;
              startWidth = $container.offsetWidth;

              const onTouchMove = (e: TouchEvent) => {
                if (!isResizing) return;
                const deltaX =
                  index % 2 === 0
                    ? -(e.touches[0].clientX - startX)
                    : e.touches[0].clientX - startX;

                const newWidth = startWidth + deltaX;

                $container.style.width = newWidth + "px";

                $img.style.width = newWidth + "px";
              };

              const onTouchEnd = () => {
                if (isResizing) {
                  isResizing = false;
                }
                dispatchNodeView();

                document.removeEventListener("touchmove", onTouchMove);
                document.removeEventListener("touchend", onTouchEnd);
              };

              document.addEventListener("touchmove", onTouchMove);
              document.addEventListener("touchend", onTouchEnd);
            },
            { passive: false }
          );
          $container.appendChild($dot);
        });
      });

      document.addEventListener("click", (e: MouseEvent) => {
        const $target = e.target as HTMLElement;
        const isClickInside =
          $container.contains($target) || $target.style.cssText === iconStyle;

        if (!isClickInside) {
          const containerStyle = $container.getAttribute("style");
          const newStyle = containerStyle?.replace(
            "border: 1px dashed #6C6C6C;",
            ""
          );
          $container.setAttribute("style", newStyle as string);

          if ($container.childElementCount > 3) {
            for (let i = 0; i < 5; i++) {
              $container.removeChild($container.lastChild as Node);
            }
          }
        }
      });

      return {
        dom: $wrapper,
      };
    };
  },
});

import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "api";
import { API_ENDPOINTS } from "./endpoints";

export const useGetReports = ({ params }: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_REPORTS, {
        params,
      });
      return response?.data;
    },
    queryKey: ["reports-list"],
  });

export const useGetReportDetails = (id: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_REPORT_DETAILS(id)
      );
      return response?.data;
    },
    queryKey: ["report-details", id],
    enabled: !!id,
  });

export const useAddReport = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      return apiClient.post(API_ENDPOINTS.ADD_REPORT, payload);
    },
  });

export const useDeleteReportMutation = () =>
  useMutation({
    mutationFn: async (id: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_REPORT(id));
    },
  });

export const useEditReport = () =>
  useMutation({
    mutationFn: async ({ id, body }: any) => {
      return apiClient.put(API_ENDPOINTS.EDIT_REPORT(id), body);
    },
  });

export const useUploadReportAsset = () =>
  useMutation({
    mutationFn: async (payload: FormData) => {
      return apiClient.put(API_ENDPOINTS.UPLOAD_REPORT_ASSET, payload);
    },
  });

export const useGetReportAssetMutation = () =>
  useMutation({
    mutationFn: async (assetId: any) => {
      return apiClient.get(API_ENDPOINTS.GET_REPORT_ASSET(assetId));
    },
  });

export const useDeleteReportAsset = () =>
  useMutation({
    mutationFn: async ({ assetId }: any) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_REPORT_ASSET(assetId));
    },
  });

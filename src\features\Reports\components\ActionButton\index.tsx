import { Button } from "react-bootstrap";

interface ActionButtonProps {
  icon: any;
  onClick?: () => void;
  title?: string;
  className?: string;
}

const ActionButton = ({
  icon,
  onClick = () => {},
  title,
  className = ""
}: ActionButtonProps) => {
  const Icon = icon;
  return (
    <Button
      onClick={onClick}
      variant=""
      title={title}
      className={`w-100 py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-between align-items-center ${className}`}
    >
      <Icon />
    </Button>
  );
};

export default ActionButton;

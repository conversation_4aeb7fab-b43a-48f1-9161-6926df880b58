import React from "react";
import { Spinner } from "react-bootstrap";
import "./ImagePlaceholder.scss";

export interface ImagePlaceholderProps {
  width?: string | number;
  height?: string | number;
  isLoading?: boolean;
  error?: string | null;
  onRetry?: () => void;
  className?: string;
}

const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({
  width = "100%",
  height = "200px",
  isLoading = false,
  error = null,
  onRetry,
  className = "",
}) => {
  const containerStyle: React.CSSProperties = {
    width: typeof width === "number" ? `${width}px` : width,
    height: typeof height === "number" ? `${height}px` : height,
  };

  return (
    <div 
      className={`image-placeholder ${className}`} 
      style={containerStyle}
    >
      <div className="image-placeholder-content">
        {isLoading ? (
          <>
            <Spinner animation="border" size="sm" />
            <span className="image-placeholder-text">Loading image...</span>
          </>
        ) : error ? (
          <>
            <div className="image-placeholder-error-icon">⚠️</div>
            <span className="image-placeholder-text">Failed to load image</span>
            {onRetry && (
              <button 
                className="image-placeholder-retry-btn"
                onClick={onRetry}
                type="button"
              >
                Retry
              </button>
            )}
          </>
        ) : (
          <>
            <div className="image-placeholder-icon">🖼️</div>
            <span className="image-placeholder-text">Image placeholder</span>
          </>
        )}
      </div>
    </div>
  );
};

export default ImagePlaceholder;
